import sqlite3
import sys

def check_all_tables(db_path="db.sqlite3"):
    """Check ALL tables in the database and their structure"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
        tables = cursor.fetchall()
        
        if not tables:
            print(f"=== DATABASE: {db_path} ===")
            print("No tables found in this database!")
            return None
        
        print(f"=== DATABASE: {db_path} ===")
        print(f"Found {len(tables)} tables:")
        
        # Show structure of all tables
        for table in tables:
            table_name = table[0]
            print(f"\n=== TABLE: {table_name} ===")
            
            # Get column info
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("Columns:")
            for col in columns:
                # col format: (cid, name, type, notnull, dflt_value, pk)
                print(f"  {col[1]} ({col[2]}){' PRIMARY KEY' if col[5] else ''}")
            
            # Show sample data
            try:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                rows = cursor.fetchall()
                
                if rows:
                    print("\nSample data (3 rows):")
                    for row in rows:
                        print(f"  {row}")
                else:
                    print("\nTable is empty")
            except sqlite3.Error as e:
                print(f"Error reading data: {e}")
        
        conn.close()
        return tables
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return None
    except FileNotFoundError:
        print(f"Database file '{db_path}' not found")
        return None

if __name__ == "__main__":
    db_path = sys.argv[1] if len(sys.argv) > 1 else "db.sqlite3"
    check_all_tables(db_path)
