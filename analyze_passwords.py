import re
from django.contrib.auth.hashers import identify_hasher

def analyze_password_hash(password_hash):
    """Analyze Django password hash format"""
    try:
        hasher = identify_hasher(password_hash)
        parts = password_hash.split('$')
        
        if len(parts) >= 4:
            algorithm = parts[0]
            iterations = parts[1] if parts[1].isdigit() else "N/A"
            salt = parts[2]
            hash_value = parts[3]
            
            return {
                'algorithm': algorithm,
                'iterations': iterations,
                'salt': salt,
                'hash': hash_value,
                'hasher_class': hasher.__class__.__name__
            }
    except Exception as e:
        return {'error': str(e)}

def read_and_analyze_users():
    """Read users from SQL dump and analyze password hashes"""
    users_data = [
        (1, 'admin', 'pbkdf2_sha256$1000000$SJ9H4p8mBDeo7AufoG5F1h$mWS1QUeJyLVBvqte/Yw9PiMKaE8SHFior/tiOutEppw='),
        (2, 'happytech', 'pbkdf2_sha256$1000000$ezV6DTCiRfLFwCxxUCgQF3$A2AQr1DIyjgZ+ueaGhp1KnOutdGb2pcyWA3xNbniPcQ='),
        (3, 'Queen', 'pbkdf2_sha256$1000000$bY60GNAquc3qS9zAi0cMEp$8K9UHOCi0/yE1APeggFEBVQZ5+ZpPOhAI+a71Iu02Z0='),
        # Add other users...
    ]
    
    print("=== PASSWORD HASH ANALYSIS ===")
    for user_id, username, password_hash in users_data:
        analysis = analyze_password_hash(password_hash)
        print(f"\nUser: {username} (ID: {user_id})")
        print(f"  Algorithm: {analysis.get('algorithm', 'Unknown')}")
        print(f"  Iterations: {analysis.get('iterations', 'Unknown')}")
        print(f"  Salt: {analysis.get('salt', 'Unknown')[:20]}...")
        print(f"  Hash: {analysis.get('hash', 'Unknown')[:20]}...")

if __name__ == "__main__":
    read_and_analyze_users()