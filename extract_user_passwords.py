#!/usr/bin/env python3
"""
Script to extract and display user information from smartit_maintenance database
"""

import re
import sys

def parse_user_data(sql_file_path):
    """Parse the SQL file and extract user information"""
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    # Find the INSERT INTO users statement
    users_pattern = r"INSERT INTO `users` VALUES (.+?);"
    match = re.search(users_pattern, content, re.DOTALL)
    
    if not match:
        print("No user data found in the SQL file")
        return
    
    users_data = match.group(1)
    
    # Parse individual user records
    # Each user record is in format: (id,'password_hash','last_login',is_superuser,'username',...),
    user_records = []
    
    # Split by '),(' to get individual records
    records = users_data.split('),(')
    
    for i, record in enumerate(records):
        # Clean up the record
        record = record.strip()
        if record.startswith('('):
            record = record[1:]
        if record.endswith(')'):
            record = record[:-1]
        
        # Split by comma, but be careful with quoted strings
        fields = []
        current_field = ""
        in_quotes = False
        quote_char = None
        
        j = 0
        while j < len(record):
            char = record[j]
            
            if not in_quotes and char in ["'", '"']:
                in_quotes = True
                quote_char = char
                current_field += char
            elif in_quotes and char == quote_char:
                # Check if it's an escaped quote
                if j + 1 < len(record) and record[j + 1] == quote_char:
                    current_field += char + char
                    j += 1
                else:
                    in_quotes = False
                    quote_char = None
                    current_field += char
            elif not in_quotes and char == ',':
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            j += 1
        
        # Add the last field
        if current_field:
            fields.append(current_field.strip())
        
        if len(fields) >= 5:  # At least id, password, last_login, is_superuser, username
            user_records.append(fields)
    
    # Display user information
    print("=" * 80)
    print("SMARTIT MAINTENANCE DATABASE - USER INFORMATION")
    print("=" * 80)
    print()
    
    for i, fields in enumerate(user_records, 1):
        try:
            user_id = fields[0]
            password_hash = fields[1].strip("'\"")
            last_login = fields[2].strip("'\"") if fields[2] != 'NULL' else 'Never'
            is_superuser = fields[3]
            username = fields[4].strip("'\"")
            first_name = fields[5].strip("'\"") if len(fields) > 5 else ''
            last_name = fields[6].strip("'\"") if len(fields) > 6 else ''
            email = fields[7].strip("'\"") if len(fields) > 7 else ''
            is_staff = fields[8] if len(fields) > 8 else ''
            is_active = fields[9] if len(fields) > 9 else ''
            date_joined = fields[10].strip("'\"") if len(fields) > 10 else ''
            user_type = fields[11].strip("'\"") if len(fields) > 11 else ''
            category = fields[12].strip("'\"") if len(fields) > 12 and fields[12] != 'NULL' else ''
            phone_number = fields[13].strip("'\"") if len(fields) > 13 else ''
            
            print(f"USER #{i}")
            print(f"  ID: {user_id}")
            print(f"  Username: {username}")
            print(f"  Email: {email}")
            print(f"  Full Name: {first_name} {last_name}".strip())
            print(f"  Phone: {phone_number}")
            print(f"  User Type: {user_type}")
            print(f"  Category: {category}")
            print(f"  Is Superuser: {'Yes' if is_superuser == '1' else 'No'}")
            print(f"  Is Staff: {'Yes' if is_staff == '1' else 'No'}")
            print(f"  Is Active: {'Yes' if is_active == '1' else 'No'}")
            print(f"  Date Joined: {date_joined}")
            print(f"  Last Login: {last_login}")
            print(f"  Password Hash: {password_hash}")
            print()
            
            # Analyze password hash
            if password_hash.startswith('pbkdf2_sha256$'):
                parts = password_hash.split('$')
                if len(parts) >= 4:
                    algorithm = parts[0]
                    iterations = parts[1]
                    salt = parts[2]
                    hash_value = parts[3]
                    
                    print(f"  PASSWORD ANALYSIS:")
                    print(f"    Algorithm: {algorithm}")
                    print(f"    Iterations: {iterations}")
                    print(f"    Salt: {salt}")
                    print(f"    Hash: {hash_value}")
                    print(f"    Security: Strong (PBKDF2 with SHA-256)")
                    print()
            
            print("-" * 60)
            print()
            
        except Exception as e:
            print(f"Error parsing user record {i}: {e}")
            continue

def main():
    sql_file = "smartit_maintenance (1).sql"
    
    print("Extracting user information from smartit_maintenance database...")
    print()
    
    parse_user_data(sql_file)
    
    print("\nNOTE: The passwords are hashed using PBKDF2-SHA256, which is a secure")
    print("hashing algorithm. These hashes cannot be easily reversed to get the")
    print("original passwords. To access accounts, you would need to:")
    print("1. Reset passwords through the application's password reset feature")
    print("2. Create new passwords if you have admin access")
    print("3. Use password recovery mechanisms if available")

if __name__ == "__main__":
    main()
