import sqlite3
import sys
import os

def import_sql_dump(sql_file_path, db_path="db.sqlite3"):
    """Import SQL dump into SQLite database"""
    try:
        # Check if SQL file exists
        if not os.path.exists(sql_file_path):
            print(f"SQL file '{sql_file_path}' not found")
            return False
            
        # Read SQL file
        with open(sql_file_path, 'r') as f:
            sql_script = f.read()
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Execute SQL script
        print(f"Importing SQL from {sql_file_path} to {db_path}...")
        cursor.executescript(sql_script)
        
        # Commit changes
        conn.commit()
        
        # Verify import
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"Import complete. Found {len(tables)} tables:")
        for table in tables:
            print(f"  - {table[0]}")
            
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"Database error during import: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python import_sql_dump.py path/to/dump.sql [path/to/database.db]")
        sys.exit(1)
        
    sql_file_path = sys.argv[1]
    db_path = sys.argv[2] if len(sys.argv) > 2 else "db.sqlite3"
    
    import_sql_dump(sql_file_path, db_path)