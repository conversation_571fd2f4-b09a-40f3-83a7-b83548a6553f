import sqlite3
import re
import sys

def convert_mysql_dump_to_sqlite(sql_file_path, db_path="smartit_maintenance.db"):
    """Convert MySQL dump to SQLite database with better error handling"""
    try:
        # Read the SQL dump
        with open(sql_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Create SQLite database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Clean up the content first
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)  # Remove comments
        content = re.sub(r'--.*?\n', '\n', content)  # Remove line comments
        
        # Extract and convert CREATE TABLE statements
        create_pattern = r'CREATE TABLE[^;]+;'
        create_tables = re.findall(create_pattern, content, re.DOTALL | re.IGNORECASE)
        
        print(f"Found {len(create_tables)} CREATE TABLE statements")
        
        for i, create_stmt in enumerate(create_tables):
            print(f"Processing table {i+1}...")
            
            # More comprehensive MySQL to SQLite conversion
            sqlite_stmt = create_stmt
            
            # Remove backticks
            sqlite_stmt = re.sub(r'`([^`]+)`', r'\1', sqlite_stmt)
            
            # Fix AUTO_INCREMENT -> AUTOINCREMENT (only for PRIMARY KEY)
            sqlite_stmt = re.sub(r'(\w+)\s+\w*int[^,\)]*\s+NOT\s+NULL\s+AUTO_INCREMENT', 
                                r'\1 INTEGER PRIMARY KEY AUTOINCREMENT', sqlite_stmt, flags=re.IGNORECASE)
            
            # Remove MySQL-specific syntax
            sqlite_stmt = re.sub(r'ENGINE=\w+[^;]*', '', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'DEFAULT CHARSET=\w+', '', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'COLLATE=\w+', '', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'AUTO_INCREMENT=\d+', '', sqlite_stmt, flags=re.IGNORECASE)
            
            # Fix data types
            sqlite_stmt = re.sub(r'\bbigint\b', 'INTEGER', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'\btinyint\(1\)', 'INTEGER', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'\bdatetime\(\d+\)', 'DATETIME', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'\blongtext\b', 'TEXT', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r'\blongblob\b', 'BLOB', sqlite_stmt, flags=re.IGNORECASE)
            
            # Remove KEY definitions that aren't PRIMARY KEY
            sqlite_stmt = re.sub(r',\s*KEY\s+[^,\)]+', '', sqlite_stmt, flags=re.IGNORECASE)
            sqlite_stmt = re.sub(r',\s*UNIQUE\s+KEY\s+[^,\)]+', '', sqlite_stmt, flags=re.IGNORECASE)
            
            # Clean up extra commas and whitespace
            sqlite_stmt = re.sub(r',\s*\)', ')', sqlite_stmt)
            sqlite_stmt = re.sub(r'\s+', ' ', sqlite_stmt)
            
            try:
                cursor.execute(sqlite_stmt)
                print(f"  ✓ Created table successfully")
            except sqlite3.Error as e:
                print(f"  ✗ Error creating table: {e}")
                print(f"    SQL: {sqlite_stmt[:100]}...")
        
        # Extract and convert INSERT statements
        insert_pattern = r'INSERT INTO[^;]+;'
        insert_statements = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        print(f"\nFound {len(insert_statements)} INSERT statements")
        
        for i, insert_stmt in enumerate(insert_statements):
            print(f"Processing insert {i+1}...")
            
            # Convert MySQL INSERT to SQLite
            sqlite_insert = re.sub(r'`([^`]+)`', r'\1', insert_stmt)
            
            # Skip problematic binary data
            if b'\\x' in sqlite_insert.encode() or 'PNG' in sqlite_insert:
                print(f"  ⚠ Skipping binary data insert")
                continue
            
            try:
                cursor.execute(sqlite_insert)
                print(f"  ✓ Inserted data successfully")
            except sqlite3.Error as e:
                print(f"  ✗ Error inserting data: {e}")
        
        conn.commit()
        
        # Verify the conversion
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"\n=== CONVERSION COMPLETE ===")
        print(f"Created {len(tables)} tables:")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"  - {table[0]} ({count} records)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    sql_file_path = sys.argv[1] if len(sys.argv) > 1 else "smartit_maintenance (1).sql"
    db_path = sys.argv[2] if len(sys.argv) > 2 else "smartit_maintenance.db"
    
    convert_mysql_dump_to_sqlite(sql_file_path, db_path)
