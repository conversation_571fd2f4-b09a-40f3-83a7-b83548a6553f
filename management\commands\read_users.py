from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Display all users and their hashed passwords'

    def handle(self, *args, **options):
        users = User.objects.all().order_by('id')
        
        self.stdout.write("=== USER CREDENTIALS ===")
        self.stdout.write(f"{'ID':<3} {'Username':<15} {'Type':<12} {'Category':<12} {'Super':<6} {'Staff':<6} {'Active':<7}")
        self.stdout.write("-" * 80)
        
        for user in users:
            self.stdout.write(
                f"{user.id:<3} {user.username:<15} {user.user_type:<12} "
                f"{user.category or 'N/A':<12} {user.is_superuser:<6} "
                f"{user.is_staff:<6} {user.is_active:<7}"
            )
            self.stdout.write(f"    Password Hash: {user.password}")
            self.stdout.write("")
