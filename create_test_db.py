import sqlite3
import sys

def create_test_database(db_path="test_db.sqlite3"):
    """Create a test database with users table"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            is_superuser INTEGER NOT NULL DEFAULT 0,
            is_staff INTEGER NOT NULL DEFAULT 0,
            is_active INTEGER NOT NULL DEFAULT 1,
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            user_type TEXT,
            category TEXT
        )
        ''')
        
        # Insert test users
        test_users = [
            (1, 'admin', 'pbkdf2_sha256$1000000$SJ9H4p8mBDeo7AufoG5F1h$mWS1QUeJyLVBvqte/Yw9PiMKaE8SHFior/tiOutEppw=', 1, 1, 1, '<EMAIL>', 'Admin', 'User', 'admin', None),
            (2, 'happytech', 'pbkdf2_sha256$1000000$ezV6DTCiRfLFwCxxUCgQF3$A2AQr1DIyjgZ+ueaGhp1KnOutdGb2pcyWA3xNbniPcQ=', 0, 0, 1, '<EMAIL>', 'Happy', 'Tech', 'technician', 'hardware'),
            (3, 'Queen', 'pbkdf2_sha256$1000000$bY60GNAquc3qS9zAi0cMEp$8K9UHOCi0/yE1APeggFEBVQZ5+ZpPOhAI+a71Iu02Z0=', 0, 0, 1, '<EMAIL>', 'Queen', 'Elizabeth', 'specialist', 'networking')
        ]
        
        cursor.executemany('''
        INSERT OR REPLACE INTO users 
        (id, username, password, is_superuser, is_staff, is_active, email, first_name, last_name, user_type, category)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_users)
        
        # Commit changes
        conn.commit()
        
        print(f"Test database created at {db_path}")
        print("Created users table with test data")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False

if __name__ == "__main__":
    db_path = sys.argv[1] if len(sys.argv) > 1 else "test_db.sqlite3"
    create_test_database(db_path)