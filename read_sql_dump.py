import re
import sys

def parse_users_from_sql_dump(sql_file_path):
    """Parse user data directly from SQL dump file"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find INSERT statements for users table
        users_pattern = r"INSERT INTO `users` VALUES \((.*?)\);"
        matches = re.findall(users_pattern, content, re.DOTALL)
        
        print("=== USERS FROM SQL DUMP ===")
        print(f"Found {len(matches)} users:")
        print()
        
        for i, match in enumerate(matches, 1):
            # Parse the values (this is simplified - real parsing would be more complex)
            # Split by comma but handle quoted strings
            values = []
            current_value = ""
            in_quotes = False
            quote_char = None
            
            for char in match:
                if char in ("'", '"') and not in_quotes:
                    in_quotes = True
                    quote_char = char
                    current_value += char
                elif char == quote_char and in_quotes:
                    in_quotes = False
                    quote_char = None
                    current_value += char
                elif char == ',' and not in_quotes:
                    values.append(current_value.strip())
                    current_value = ""
                else:
                    current_value += char
            
            if current_value.strip():
                values.append(current_value.strip())
            
            if len(values) >= 3:  # At least ID, username, password
                user_id = values[0]
                username = values[4].strip("'\"") if len(values) > 4 else "Unknown"
                password = values[1].strip("'\"") if len(values) > 1 else "Unknown"
                
                print(f"User {i}:")
                print(f"  ID: {user_id}")
                print(f"  Username: {username}")
                print(f"  Password Hash: {password}")
                print()
        
        return matches
        
    except FileNotFoundError:
        print(f"SQL file '{sql_file_path}' not found")
        return None
    except Exception as e:
        print(f"Error parsing SQL file: {e}")
        return None

def extract_users_simple(sql_file_path):
    """Simple extraction using regex patterns"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Look for the users table data section
        users_section = re.search(r'INSERT INTO `users` VALUES.*?;', content, re.DOTALL)
        
        if users_section:
            print("=== USERS DATA FOUND ===")
            print(users_section.group(0)[:1000] + "..." if len(users_section.group(0)) > 1000 else users_section.group(0))
        else:
            print("No users INSERT statement found in SQL dump")
            
        # Also look for any password-related data
        password_patterns = [
            r'pbkdf2_sha256\$[^\'\"]*',
            r'INSERT INTO.*user.*VALUES.*',
        ]
        
        for pattern in password_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"\nFound {len(matches)} password hashes:")
                for match in matches[:5]:  # Show first 5
                    print(f"  {match}")
                    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    sql_file_path = sys.argv[1] if len(sys.argv) > 1 else "smartit_maintenance (1).sql"
    
    print("Choose option:")
    print("1. Parse users data")
    print("2. Simple extraction")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        extract_users_simple(sql_file_path)
    else:
        parse_users_from_sql_dump(sql_file_path)