import sqlite3
import sys

def read_users_from_db(db_path="db.sqlite3"):
    """Read users and their hashed passwords from SQLite database"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, username, password, is_superuser, is_staff, is_active, 
                   first_name, last_name, email, user_type, category
            FROM users 
            ORDER BY id
        """)
        
        users = cursor.fetchall()
        
        print("=== USER CREDENTIALS ===")
        print(f"{'ID':<3} {'Username':<15} {'Name':<20} {'Type':<12} {'Category':<12} {'Super':<6} {'Staff':<6} {'Active':<7}")
        print("-" * 100)
        
        for user in users:
            user_id, username, password, is_super, is_staff, is_active, first_name, last_name, email, user_type, category = user
            full_name = f"{first_name} {last_name}".strip()
            print(f"{user_id:<3} {username:<15} {full_name:<20} {user_type:<12} {category or 'N/A':<12} {bool(is_super):<6} {bool(is_staff):<6} {bool(is_active):<7}")
            print(f"    Email: {email}")
            print(f"    Password Hash: {password}")
            print()
        
        conn.close()
        return users
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return None
    except FileNotFoundError:
        print(f"Database file '{db_path}' not found")
        return None

def read_users_simple(db_path="db.sqlite3"):
    """Simple version - just username and password"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, password FROM users ORDER BY id")
        users = cursor.fetchall()
        
        print("=== SIMPLE USER LIST ===")
        for user_id, username, password in users:
            print(f"ID: {user_id} | Username: {username}")
            print(f"Password Hash: {password}")
            print("-" * 50)
        
        conn.close()
        return users
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return None

if __name__ == "__main__":
    db_path = sys.argv[1] if len(sys.argv) > 1 else "db.sqlite3"
    
    print("Choose option:")
    print("1. Detailed user info")
    print("2. Simple username/password list")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        read_users_simple(db_path)
    else:
        read_users_from_db(db_path)
